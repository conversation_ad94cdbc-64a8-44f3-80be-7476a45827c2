<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI需求规范与项目管理课程 | 教学手册</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&family=Noto+Serif+SC:wght@400;700;900&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.0/dist/mermaid.min.js"></script>
    <style>
        :root {
            --primary-color: #3B82F6;
            --primary-dark: #1E40AF;
            --primary-light: #DBEAFE;
            --secondary-color: #6366F1;
            --accent-color: #F59E0B;
            --accent-light: #FEF3C7;
            --text-color: #1F2937;
            --text-light-color: #6B7280;
            --text-lighter-color: #9CA3AF;
            --bg-color: #F9FAFB;
            --card-color: #FFFFFF;
            --border-color: #E5E7EB;
            --border-light: #F3F4F6;
            --highlight-bg: rgba(219, 234, 254, 0.6);
            --highlight-border: rgba(59, 130, 246, 0.3);
            --code-bg: #F3F4F6;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
        }

        [data-theme="dark"] {
            --primary-color: #60A5FA;
            --primary-dark: #93C5FD;
            --primary-light: #1E3A8A;
            --secondary-color: #A5B4FC;
            --accent-color: #FBBF24;
            --accent-light: #92400E;
            --text-color: #F9FAFB;
            --text-light-color: #D1D5DB;
            --text-lighter-color: #9CA3AF;
            --bg-color: #111827;
            --card-color: #1F2937;
            --border-color: #374151;
            --border-light: #1F2937;
            --highlight-bg: rgba(30, 58, 138, 0.5);
            --highlight-border: rgba(96, 165, 250, 0.3);
            --code-bg: #374151;
            --success-color: #34D399;
            --warning-color: #FBBF24;
            --error-color: #F87171;
        }

        /* 基础样式 */
        body {
            font-family: "Noto Sans SC", sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.7;
            transition: background-color 0.3s, color 0.3s;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: "Noto Serif SC", serif;
            font-weight: 700;
            line-height: 1.3;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        /* 导航样式 */
        .nav-shadow {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        /* 英雄区样式 */
        .hero-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .hero-wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
        }

        .hero-wave svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px);
            height: 58px;
        }

        .hero-wave .shape-fill {
            fill: var(--bg-color);
        }

        /* 卡片样式 */
        .card {
            background-color: var(--card-color);
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: transform 0.3s, box-shadow 0.3s;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* 模块卡片样式 */
        .module-card {
            border-left: 4px solid var(--primary-color);
            margin-bottom: 1.5rem;
            border-radius: 0 0.5rem 0.5rem 0;
            background-color: var(--card-color);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .module-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left-color: var(--accent-color);
        }

        .module-header {
            cursor: pointer;
            padding: 1.25rem;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .module-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
            display: none;
            border-top: 1px solid var(--border-light);
        }

        .module-content.active {
            display: block;
        }

        /* 文本样式 */
        .text-gradient {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
        }

        .first-letter-drop:first-letter {
            initial-letter: 2;
            -webkit-initial-letter: 2;
            color: var(--primary-color);
            font-family: "Noto Serif SC", serif;
            font-weight: 900;
            margin-right: 0.1em;
            float: left;
            font-size: 3.5em;
            line-height: 0.8;
            padding-right: 0.1em;
        }

        /* 引用样式 */
        .quote-box {
            position: relative;
            padding: 1.5rem;
            background-color: var(--highlight-bg);
            border-left: 4px solid var(--primary-color);
            margin: 1.5rem 0;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .quote-box::before {
            content: """;
            font-family: "Noto Serif SC", serif;
            font-size: 4rem;
            line-height: 0;
            position: absolute;
            top: 20px;
            left: 10px;
            color: var(--primary-color);
            opacity: 0.3;
        }

        .quote-content {
            margin-left: 2rem;
            font-style: italic;
        }

        .quote-author {
            text-align: right;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        /* 活动卡片 */
        .activity-card {
            border-left: 4px solid var(--accent-color);
            padding: 1rem 1.25rem;
            background-color: var(--accent-light);
            background-opacity: 0.1;
            margin: 1.5rem 0;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .activity-title {
            font-weight: 600;
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            text-align: center;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .btn-outline {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* 图标盒子 */
        .icon-box {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.5rem;
        }

        .icon-circle {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            margin-right: 1rem;
            color: var(--primary-color);
        }

        /* 特性卡片 */
        .feature-card {
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: var(--card-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        /* 时间线 */
        .timeline {
            position: relative;
            padding-left: 2rem;
            margin: 2rem 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: var(--primary-color);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: -2.4rem;
            top: 0.3rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: var(--primary-color);
        }

        .timeline-content {
            padding-bottom: 1rem;
        }

        /* 表格样式 */
        .custom-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
            overflow: hidden;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .custom-table th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            text-align: left;
            padding: 1rem;
        }

        .custom-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--card-color);
        }

        .custom-table tr:last-child td {
            border-bottom: none;
        }

        /* 深色模式切换 */
        .theme-toggle {
            position: relative;
            width: 48px;
            height: 24px;
            border-radius: 24px;
            background-color: var(--border-color);
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .theme-toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: white;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .theme-toggle.dark {
            background-color: var(--primary-color);
        }

        .theme-toggle.dark::after {
            transform: translateX(24px);
        }

        /* 优先级矩阵 */
        .priority-matrix {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .matrix-cell {
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: var(--card-color);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .high-high {
            background-color: var(--success-color);
            color: white;
        }

        .high-low {
            background-color: var(--warning-color);
            color: white;
        }

        .low-high {
            background-color: var(--primary-light);
        }

        .low-low {
            background-color: var(--error-color);
            color: white;
        }

        /* 流程图 */
        .process-flow {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 2rem 0;
        }

        .process-step {
            background-color: var(--card-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            width: 80%;
            text-align: center;
            margin-bottom: 1rem;
            position: relative;
        }

        .process-step::after {
            content: '';
            position: absolute;
            bottom: -1.5rem;
            left: 50%;
            width: 2px;
            height: 1rem;
            background-color: var(--border-color);
            transform: translateX(-50%);
        }

        .process-step:last-child::after {
            display: none;
        }

        /* 回到顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transition: opacity 0.3s, transform 0.3s;
            z-index: 100;
        }

        .back-to-top.visible {
            opacity: 1;
        }

        .back-to-top:hover {
            transform: translateY(-5px);
        }

        /* 标签样式 */
        .tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        [data-theme="dark"] .tag {
            background-color: var(--primary-dark);
            color: var(--primary-light);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .module-header {
                padding: 1rem;
            }
            
            .module-content {
                padding: 0 1rem 1rem 1rem;
            }
            
            .first-letter-drop:first-letter {
                font-size: 3em;
            }
            
            .timeline {
                padding-left: 1.5rem;
            }
            
            .timeline-dot {
                left: -1.9rem;
            }
            
            .process-step {
                width: 100%;
            }
        }

        /* 打印样式 */
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                font-size: 12pt;
            }
            
            .card, .feature-card, .module-card {
                box-shadow: none !important;
                border: 1px solid #ccc;
            }
            
            .container {
                width: 100%;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="sticky top-0 z-50 nav-shadow bg-white dark:bg-gray-900 transition-colors duration-300">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-graduation-cap text-2xl text-blue-500 mr-3"></i>
                    <span class="font-bold text-xl">AI转型教学中心</span>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#overview" class="hover:text-blue-500 transition-colors">课程总览</a>
                    <a href="#course1" class="hover:text-blue-500 transition-colors">需求规范</a>
                    <a href="#course2" class="hover:text-blue-500 transition-colors">项目管理</a>
                    <a href="#conclusion" class="hover:text-blue-500 transition-colors">结论建议</a>
                    <div class="theme-toggle" id="theme-toggle"></div>
                </div>
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-500 hover:text-blue-500 focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            <!-- 移动端菜单 -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 pb-2">
                <a href="#overview" class="block py-2 hover:text-blue-500 transition-colors">课程总览</a>
                <a href="#course1" class="block py-2 hover:text-blue-500 transition-colors">需求规范</a>
                <a href="#course2" class="block py-2 hover:text-blue-500 transition-colors">项目管理</a>
                <a href="#conclusion" class="block py-2 hover:text-blue-500 transition-colors">结论建议</a>
                <div class="flex items-center justify-between mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                    <span>深色模式</span>
                    <div class="theme-toggle" id="mobile-theme-toggle"></div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区 -->
    <div class="hero-gradient text-white relative">
        <div class="container mx-auto px-4 py-16 md:py-24">
            <div class="max-w-3xl">
                <div class="inline-block bg-white bg-opacity-20 rounded-full px-4 py-1 mb-6">
                    <span class="text-sm font-semibold">企业AI转型课程系列</span>
                </div>
                <h1 class="text-4xl md:text-5xl font-bold mb-4 leading-tight">驾驭AI转型之旅：从业务愿景到价值落地</h1>
                <p class="text-xl md:text-2xl opacity-90 mb-8">AI的专业化需求与敏捷化项目管理之道</p>
                <div class="flex flex-wrap gap-4">
                    <a href="#overview" class="btn bg-white text-blue-600 hover:bg-gray-100 transition-all shadow-lg">
                        <i class="fas fa-book-reader mr-2"></i>了解课程
                    </a>
                    <a href="#content-structure" class="btn border-2 border-white hover:bg-white hover:bg-opacity-10 transition-all">
                        <i class="fas fa-sitemap mr-2"></i>课程架构
                    </a>
                </div>
            </div>
        </div>
        <!-- 波浪效果 -->
        <div class="hero-wave">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
            </svg>
        </div>
    </div>

    <!-- 主内容区 -->
    <main class="container mx-auto px-4 py-12">
        <!-- 课程总览 -->
        <section id="overview" class="mb-16">
            <h2 class="text-3xl font-bold mb-8 text-gradient">课程教学手册：AI需求规范与项目管理</h2>
            
            <div class="card p-8 mb-12">
                <h3 class="text-2xl font-bold mb-6 text-center">课程总览与教学主线</h3>
                
                <p class="first-letter-drop mb-6">
                    本系列课程旨在引导企业业务人员深入理解人工智能（AI）项目在需求规范和项目管理方面的独特性，通过引导式的教学设计，帮助学员自主发现做事的逻辑和方向，从而掌握实用的方法论。课程主线设定为："驾驭AI转型之旅：从业务愿景到价值落地——AI的专业化需求与敏捷化项目管理之道"。
                </p>
                
                <p class="mb-8">
                    这一主线强调AI项目不仅是技术革新，更是一场业务转型的旅程，需要学习和适应。它清晰地指出了课程的起点（业务愿景）和终点（可衡量的业务价值），这对于业务背景的学员至关重要。同时，主线明确点出AI项目需要"专业化"的需求方法和"敏捷化"的项目管理策略，预示着课程内容将在传统知识体系基础上进行创新和深化。
                </p>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clipboard-list text-xl"></i>
                        </div>
                        <h4 class="text-xl font-bold mb-4">课程一：AI需求规范</h4>
                        <p class="text-gray-600 dark:text-gray-300">从业务意图到技术实现：专注于AI项目独特的需求定义、捕获、分析和文档化过程。</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tasks text-xl"></i>
                        </div>
                        <h4 class="text-xl font-bold mb-4">课程二：AI项目管理</h4>
                        <p class="text-gray-600 dark:text-gray-300">从项目规划到价值实现：聚焦于AI项目从规划、执行、监控到价值交付的全生命周期管理。</p>
                    </div>
                </div>
                
                <div class="mt-8 text-center">
                    <p class="italic text-gray-600 dark:text-gray-300">两门课程紧密衔接，共同构成了企业成功实施AI项目所需的核心能力框架。</p>
                </div>
            </div>
            
            <div id="content-structure" class="mb-12">
                <h3 class="text-2xl font-bold mb-6">课程结构与内容</h3>
                
                <div class="flex flex-col md:flex-row gap-8">
                    <div class="md:w-1/2">
                        <div class="card p-6">
                            <h4 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">《AI需求规范》</h4>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0">
                                        <span>1</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">理解AI需求的独特性</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">探索AI需求与传统软件需求的本质区别，学习构建有效AI需求的四个基石。</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0">
                                        <span>2</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">AI需求的分类与结构化</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">掌握业务目标与能力需求框架，理解AI系统的功能与非功能需求，以及数据需求的系统化捕获。</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0">
                                        <span>3</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">需求文档化与多方评审</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">学习AI需求文档的标准结构，掌握跨角色需求评审方法，了解不同类型AI项目的需求差异。</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0">
                                        <span>4</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">需求实战与应用</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">规避常见需求陷阱，通过实战练习编写完整AI需求，学习需求到项目管理的衔接。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="md:w-1/2">
                        <div class="card p-6">
                            <h4 class="text-xl font-bold mb-4 text-indigo-600 dark:text-indigo-400">《AI项目管理》</h4>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-3 flex-shrink-0">
                                        <span>1</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">需求转化为项目计划</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">学习需求到行动计划的转换，掌握AI项目的特殊规划要素，通过案例研讨理解需求驱动的项目规划。</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-3 flex-shrink-0">
                                        <span>2</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">AI项目执行与控制</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">理解适合AI项目的管理模型，掌握迭代与适应性管理方法，学习跨领域团队的协作管理。</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-3 flex-shrink-0">
                                        <span>3</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">多项目与资源管理</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">区分不同类型AI项目的管理重点，学习资源优化与知识共享，掌握多项目的协调机制。</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-3 flex-shrink-0">
                                        <span>4</span>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold">项目收尾与价值评估</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">了解AI系统的部署与转交，学习价值评估与经验总结，通过综合实战演练巩固所学。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card p-8">
                <h3 class="text-2xl font-bold mb-6">本教学手册使用说明</h3>
                
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div class="icon-box">
                        <div class="icon-circle">
                            <i class="fas fa-bullseye text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold mb-2">手册定位</h4>
                            <p class="text-gray-600 dark:text-gray-300">本手册旨在为讲师提供详尽的、步骤化的教学指导，以确保两门关于AI需求与项目管理的课程能够以高度互动和实用的方式，有效地传递给企业业务人员。</p>
                        </div>
                    </div>
                    
                    <div class="icon-box">
                        <div class="icon-circle">
                            <i class="fas fa-lightbulb text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold mb-2">教学理念</h4>
                            <p class="text-gray-600 dark:text-gray-300">课程设计遵循成人学习的核心原则，强调经验学习和以问题为中心。通过引导式发现、案例研讨、实战演练等方式，激发学员的自主学习能力。</p>
                        </div>
                    </div>
                </div>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="icon-box">
                        <div class="icon-circle">
                            <i class="fas fa-comments text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold mb-2">互动与引导</h4>
                            <p class="text-gray-600 dark:text-gray-300">手册中的"互动教学设计"部分是课程的灵魂。讲师应熟练运用苏格拉底式的提问、建设性的反馈等引导技巧，鼓励学员思考、讨论、分享，并从集体的智慧中学习。营造一个安全的学习氛围，允许学员探索、犯错并从中成长至关重要。</p>
                        </div>
                    </div>
                    
                    <div class="icon-box">
                        <div class="icon-circle">
                            <i class="fas fa-sync-alt text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold mb-2">融合传统与创新</h4>
                            <p class="text-gray-600 dark:text-gray-300">课程内容将基于业界公认的知识体系，如IIBA的BABOK、PMI的PMP等，为学员提供坚实的理论基础。在此基础上，重点阐述AI项目的特殊性，以及如何在传统框架上进行创新和调整，以适应AI项目的独特挑战。这种设计旨在降低学习门槛，帮助学员将新知识与已有经验相结合，提升学习效果。</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8">
                    <div class="icon-box">
                        <div class="icon-circle">
                            <i class="fas fa-sitemap text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-bold mb-2">内容结构</h4>
                            <p class="text-gray-600 dark:text-gray-300">针对课程大纲中的每个模块和子模块，本手册均提供了PPT核心内容点、知识点讲解思路，以及精心设计的互动教学环节。以下部分将详细介绍各模块的具体内容。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 课程一：AI需求规范 -->
        <section id="course1" class="mb-16">
            <h2 class="text-3xl font-bold mb-8 text-gradient">第一部分：《AI需求规范：从业务意图到技术实现》</h2>
            <p class="text-xl mb-8 text-gray-600 dark:text-gray-300 italic">总时长：3小时</p>
            
            <!-- 模块一 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块一：理解AI需求的独特性 (40分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <div class="quote-box mb-6">
                        <div class="quote-content">
                            "对任何思考者来说，最大的挑战在于用一种能够找到解决方案的方式来陈述问题。"
                        </div>
                        <div class="quote-author">— 改编自伯特兰·罗素</div>
                    </div>
                    
                    <h4 class="text-xl font-semibold mb-4">模块导入</h4>
                    <p class="mb-6">
                        开场时，首先肯定学员们对传统软件需求的理解。随即抛出核心问题："既然我们都熟悉传统需求，为何还需要专门探讨'AI需求'？它仅仅是更复杂的软件需求吗？"以此激发学员的好奇心和思考，为后续的"自主发现"铺路。
                    </p>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-users mr-2"></i>互动教学：快速调研 / 思考-配对-分享
                        </div>
                        <p class="mb-2 font-medium">引导问题：</p>
                        <p class="text-gray-700 dark:text-gray-300">"根据您的经验或了解，您认为AI项目与传统IT项目在'需求'层面，最大的不同可能是什么？或者您预见到哪些潜在的差异和挑战？"</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">1.1 传统需求与AI需求的本质区别 (18分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-6">
                            <h5 class="font-bold text-lg mb-4 text-center">传统软件特性</h5>
                            <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                <li><i class="fas fa-check-circle text-green-500 mr-2"></i>确定性输出</li>
                                <li><i class="fas fa-check-circle text-green-500 mr-2"></i>基于规则</li>
                                <li><i class="fas fa-check-circle text-green-500 mr-2"></i>显式编程</li>
                                <li><i class="fas fa-check-circle text-green-500 mr-2"></i>功能相对静态（除非进行版本更新）</li>
                            </ul>
                        </div>
                        <div class="card p-6">
                            <h5 class="font-bold text-lg mb-4 text-center">AI系统特性</h5>
                            <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                <li><i class="fas fa-brain text-blue-500 mr-2"></i>概率性输出</li>
                                <li><i class="fas fa-brain text-blue-500 mr-2"></i>数据驱动与学习</li>
                                <li><i class="fas fa-brain text-blue-500 mr-2"></i>随数据演化</li>
                                <li><i class="fas fa-brain text-blue-500 mr-2"></i>持续学习特性</li>
                                <li><i class="fas fa-brain text-blue-500 mr-2"></i>通常具有"黑箱"特性</li>
                            </ul>
                        </div>
                    </div>
                    
                    <p class="mb-6">
                        传统软件如同"固定的蓝图"，而AI系统则更像"生长的植物"，会随着数据和环境的变化而不断演化。
                    </p>
                    
                    <h5 class="font-bold text-lg mb-4">核心差异点</h5>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="card p-4 text-center">
                            <i class="fas fa-random text-3xl text-blue-500 mb-4"></i>
                            <p class="font-semibold">不确定性与概率性</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">AI输出结果带有概率性</p>
                        </div>
                        <div class="card p-4 text-center">
                            <i class="fas fa-database text-3xl text-blue-500 mb-4"></i>
                            <p class="font-semibold">数据依赖性</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">AI性能直接依赖于数据质量</p>
                        </div>
                        <div class="card p-4 text-center">
                            <i class="fas fa-sync-alt text-3xl text-blue-500 mb-4"></i>
                            <p class="font-semibold">持续学习与模型漂移</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">AI系统随时间演化</p>
                        </div>
                        <div class="card p-4 text-center">
                            <i class="fas fa-search text-3xl text-blue-500 mb-4"></i>
                            <p class="font-semibold">透明度与可解释性</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">AI决策过程需要被解释</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-check-circle mr-2"></i>互动教学："AI神话 vs. 现实"快速判断
                        </div>
                        <p class="mb-2 font-medium">引导问题/陈述 (判断对错并讨论)：</p>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>"AI系统总能给出唯一正确的答案。" <span class="text-red-500 font-semibold">（错误 - 概率性）</span></li>
                            <li>"一旦AI模型构建完成，其需求就固定不变了。" <span class="text-red-500 font-semibold">（错误 - 持续学习，模型漂移）</span></li>
                            <li>"AI需求最重要的部分是定义算法。" <span class="text-red-500 font-semibold">（错误 - 对业务分析师/项目经理而言，业务问题、数据和预期成果更为关键）</span></li>
                            <li>"如果AI犯了错，总能轻易找出原因。" <span class="text-red-500 font-semibold">（错误 - 黑箱特性，需要XAI）</span></li>
                        </ol>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">1.2 构建有效AI需求的四个基石 (12分钟)</h4>
                    
                    <div class="mb-8">
                        <div class="card p-6 text-center mb-6">
                            <h5 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">有效的AI需求</h5>
                            <p class="text-gray-600 dark:text-gray-400">以下四大支柱共同支撑有效的AI需求框架</p>
                        </div>
                        
                        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="card p-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-bullseye text-3xl text-blue-500"></i>
                                </div>
                                <h6 class="font-bold text-center mb-2">业务问题驱动</h6>
                                <p class="text-sm text-center text-gray-600 dark:text-gray-400">"始于'为何做'，而非'AI能做什么'。"</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-database text-3xl text-blue-500"></i>
                                </div>
                                <h6 class="font-bold text-center mb-2">数据视角的前置考虑</h6>
                                <p class="text-sm text-center text-gray-600 dark:text-gray-400">"数据是燃料，设计伊始即融入数据思维。"</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-flag text-3xl text-blue-500"></i>
                                </div>
                                <h6 class="font-bold text-center mb-2">关注期望结果而非实现方式</h6>
                                <p class="text-sm text-center text-gray-600 dark:text-gray-400">"定义'成功标准'，而非具体算法。"</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="text-center mb-4">
                                    <i class="fas fa-balance-scale text-3xl text-blue-500"></i>
                                </div>
                                <h6 class="font-bold text-center mb-2">伦理维度的需求融入</h6>
                                <p class="text-sm text-center text-gray-600 dark:text-gray-400">"从源头构建负责任的AI。"</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-toolbox mr-2"></i>互动教学："基石稳固性测试" - 微型场景分析
                        </div>
                        <p class="mb-4">针对每个基石，呈现一个简短的AI项目构想场景。提问："此场景中哪个基石被忽视了？团队首先应该提出的更恰当的问题是什么？"</p>
                        <div class="p-4 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 rounded-lg">
                            <p class="italic text-gray-700 dark:text-gray-300">
                                <span class="font-semibold">示例场景：</span>"某公司希望构建一个AI来预测股价，因为AI是时下热门技术。"
                            </p>
                            <p class="mt-2 text-gray-600 dark:text-gray-400">
                                <span class="font-semibold">被忽视的基石：</span>业务问题驱动
                            </p>
                            <p class="mt-1 text-gray-600 dark:text-gray-400">
                                <span class="font-semibold">更恰当的问题：</span>我们试图通过这种预测来改善或规避哪个具体的财务决策或风险？
                            </p>
                        </div>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">1.3 案例学习：AI需求的常见问题 (10分钟)</h4>
                    
                    <h5 class="font-bold text-lg mb-4 text-red-600 dark:text-red-400">从失败中学习：AI需求的常见陷阱</h5>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5 border-l-4 border-red-500">
                            <h6 class="font-bold mb-2 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                需求模糊或无法测试
                            </h6>
                            <p class="text-sm mb-2"><span class="font-semibold">表象症状：</span>团队无法明确判断AI系统是否符合预期</p>
                            <p class="text-sm"><span class="font-semibold">根本原因：</span>未能设定明确、可衡量的成功指标</p>
                        </div>
                        
                        <div class="card p-5 border-l-4 border-red-500">
                            <h6 class="font-bold mb-2 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                忽视数据问题
                            </h6>
                            <p class="text-sm mb-2"><span class="font-semibold">表象症状：</span>模型性能不佳，项目延迟</p>
                            <p class="text-sm"><span class="font-semibold">根本原因：</span>未在需求阶段充分评估数据质量与可用性</p>
                        </div>
                        
                        <div class="card p-5 border-l-4 border-red-500">
                            <h6 class="font-bold mb-2 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                不切实际的性能预期
                            </h6>
                            <p class="text-sm mb-2"><span class="font-semibold">表象症状：</span>干系人对结果不满意，尽管技术上已达到可行的最佳结果</p>
                            <p class="text-sm"><span class="font-semibold">根本原因：</span>未能理解AI的概率性本质，设定了过高的准确率期望</p>
                        </div>
                        
                        <div class="card p-5 border-l-4 border-red-500">
                            <h6 class="font-bold mb-2 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                忽视伦理或可解释性需求
                            </h6>
                            <p class="text-sm mb-2"><span class="font-semibold">表象症状：</span>模型上线后出现偏见问题或决策无法解释</p>
                            <p class="text-sm"><span class="font-semibold">根本原因：</span>在需求阶段未将伦理考量作为核心需求</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-search mr-2"></i>互动教学："火眼金睛"——微型案例剖析
                        </div>
                        <p>呈现一个段落长度的AI项目情景描述。提问："此情景中最关键的需求缺陷是什么？'四大基石'中的哪一条原则本可以帮助避免这个问题？"</p>
                    </div>
                </div>
            </div>
            
            <!-- 模块二 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块二：AI需求的分类与结构化 (50分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <div class="quote-box mb-6">
                        <div class="quote-content">
                            "如果我有一个小时来解决一个问题，我会花55分钟思考问题本身，5分钟思考解决方案。"
                        </div>
                        <div class="quote-author">— 阿尔伯特·爱因斯坦</div>
                    </div>
                    
                    <p class="mb-6">
                        承接上一模块，从"为何AI需求与众不同"过渡到"我们需要定义哪些类型的AI需求"以及"如何系统化地构建这些需求"。强调结构化的方法有助于管理AI需求的复杂性，并确保覆盖所有关键方面。
                    </p>
                    
                    <h4 class="text-xl font-semibold mb-4">2.1 业务目标与能力需求框架 (15分钟)</h4>
                    
                    <div class="process-flow mb-8">
                        <div class="process-step font-semibold">业务问题</div>
                        <div class="process-step font-semibold">业务目标</div>
                        <div class="process-step font-semibold">AI项目目标</div>
                        <div class="process-step font-semibold">所需AI能力</div>
                        <div class="process-step font-semibold">可衡量的成功指标 (KPIs)</div>
                    </div>
                    
                    <h5 class="font-bold text-lg mb-4">可衡量的成功指标</h5>
                    <div class="grid md:grid-cols-3 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-chart-line text-3xl text-blue-500"></i>
                            </div>
                            <h6 class="font-bold text-center mb-2">业务KPIs</h6>
                            <ul class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                <li>• 收入增长</li>
                                <li>• 成本降低</li>
                                <li>• 客户满意度提升</li>
                            </ul>
                        </div>
                        
                        <div class="card p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-brain text-3xl text-blue-500"></i>
                            </div>
                            <h6 class="font-bold text-center mb-2">AI模型性能指标</h6>
                            <ul class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                <li>• 准确率</li>
                                <li>• 精确率、召回率</li>
                                <li>• F1分数、延迟</li>
                            </ul>
                        </div>
                        
                        <div class="card p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-cogs text-3xl text-blue-500"></i>
                            </div>
                            <h6 class="font-bold text-center mb-2">运营指标</h6>
                            <ul class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                <li>• 处理时间缩短</li>
                                <li>• 错误率降低</li>
                                <li>• 自动化水平提高</li>
                            </ul>
                        </div>
                    </div>
                    
                    <h5 class="font-bold text-lg mb-4">优先级排序框架：价值 vs. 可行性矩阵</h5>
                    <div class="priority-matrix mb-6">
                        <div class="matrix-cell high-high">
                            <p class="font-bold mb-1">高价值 / 高可行性</p>
                            <p class="text-sm">优先实施（速赢）</p>
                        </div>
                        <div class="matrix-cell high-low">
                            <p class="font-bold mb-1">高价值 / 低可行性</p>
                            <p class="text-sm">战略性投入</p>
                        </div>
                        <div class="matrix-cell low-high">
                            <p class="font-bold mb-1">低价值 / 高可行性</p>
                            <p class="text-sm">次优选择</p>
                        </div>
                        <div class="matrix-cell low-low">
                            <p class="font-bold mb-1">低价值 / 低可行性</p>
                            <p class="text-sm">避免投入</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-brain mr-2"></i>互动教学："KPI头脑风暴与优先级排序"
                        </div>
                        <p class="mb-2 font-medium">给定一个AI项目案例（例如："AI预测设备故障"）：</p>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>"该项目可能影响哪些2-3个关键的'业务'KPI？"</li>
                            <li>"哪些2-3个'AI模型性能'指标能表明AI运行良好？"</li>
                            <li>"这个预测性维护AI的'最小可行能力'会是什么？"</li>
                            <li>"如果'所有设备类型的数据可用性'较低（低可行性），但'预测关键设备A型故障'的价值很高，这个用例在价值/可行性矩阵中会处于哪个位置？"</li>
                        </ol>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">2.2 AI系统的功能与非功能需求 (20分钟)</h4>
                    
                    <div class="mb-6">
                        <h5 class="font-bold text-lg mb-4">AI功能需求 (Functional Requirements, FRs)</h5>
                        <div class="card p-6 mb-4">
                            <h6 class="font-semibold mb-2">AI用户故事格式:</h6>
                            <p class="italic text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                "作为一名[用户类型]，我希望[AI能力]，以便[收益]，基于这些数据特性并预期得到一个概率性结果。"
                            </p>
                        </div>
                        
                        <div class="card p-6">
                            <h6 class="font-semibold mb-3">描述概率性场景:</h6>
                            <div class="space-y-3 text-gray-700 dark:text-gray-300">
                                <p class="bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 p-2 rounded-lg">"系统应能以X%的置信度识别潜在欺诈。"</p>
                                <p class="bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 p-2 rounded-lg">"当置信度低于Y%时，应上报人工审核。"</p>
                                <p class="bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 p-2 rounded-lg">"聊天机器人应能以90%的准确率理解与订单状态相关的用户意图。"</p>
                                <p class="bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 p-2 rounded-lg">"图像识别模型应能以95%的精确率对关键产品缺陷进行分类。"</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h5 class="font-bold text-lg mb-4">AI特定的非功能需求 (Non-Functional Requirements, NFRs)</h5>
                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="card p-5">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-tachometer-alt text-xl text-blue-500 mr-2"></i>
                                    <h6 class="font-semibold">性能 (Performance)</h6>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300">延迟（预测速度）、吞吐量（每秒预测次数）、模型训练时间。</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-database text-xl text-blue-500 mr-2"></i>
                                    <h6 class="font-semibold">AI的数据质量</h6>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300">训练/输入数据的准确性、完整性、一致性、及时性、代表性。</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-search text-xl text-blue-500 mr-2"></i>
                                    <h6 class="font-semibold">可解释性 (Explainability)</h6>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300">系统提供其输出理由的需求（例如，特征重要性、决策路径）。</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-balance-scale text-xl text-blue-500 mr-2"></i>
                                    <h6 class="font-semibold">伦理NFRs</h6>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300">公平性（例如，"模型预测不应对人口统计学群体A、B、C显示出统计学上显著的偏见"）、问责制、透明度。</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-tools text-xl text-blue-500 mr-2"></i>
                                    <h6 class="font-semibold">模型可维护性</h6>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300">模型性能监控、漂移检测、再训练支持、模型版本控制的需求。</p>
                            </div>
                            
                            <div class="card p-5">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-shield-alt text-xl text-blue-500 mr-2"></i>
                                    <h6 class="font-semibold">鲁棒性/安全性</h6>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300">对对抗性攻击、数据投毒的抵御能力。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-magnifying-glass mr-2"></i>互动教学："AI系统的NFR侦探"
                        </div>
                        <p class="mb-2 font-medium">给出一个AI系统的功能需求：</p>
                        <p class="italic mb-4 text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            "作为一名医生，我希望AI能够根据医学影像辅助诊断特定疾病，以便我能更快、更准确地制定治疗方案。"
                        </p>
                        <p class="mb-2 font-medium">请学员分组讨论：</p>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>"针对这个系统，有哪些2-3个关键的'AI特定NFR'？（从性能、数据、可解释性、伦理、可维护性等方面考虑）"</li>
                            <li>"您会如何表述其中一个NFR，使其具有可衡量性/可测试性？"</li>
                        </ol>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">2.3 数据需求的系统化捕获 (15分钟)</h4>
                    
                    <div class="quote-box mb-6">
                        <div class="quote-content">
                            "数据是新石油"——但它需要提炼。
                        </div>
                    </div>
                    
                    <h5 class="font-bold text-lg mb-4">全面的数据需求评估框架</h5>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-download text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">来源与获取</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">数据从何而来？（内部、外部、新采集）。如何获取？</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-chart-pie text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">数量</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">训练、验证、测试需要多少数据？（大数据有助于学习复杂模式）</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-layer-group text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">多样性</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">结构化、非结构化、半结构化？不同的数据类型？</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-bolt text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">速度</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">数据到达/变化的频率？实时还是批量？</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-check-double text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">真实性/质量</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">准确性、完整性、一致性、及时性、相关性、无偏见性</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-file-code text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">格式与结构</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">所需的数据格式、模式。</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-tags text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">标注</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">是否需要标签？标注规范/指南是什么？</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-database text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">存储与可访问性</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">数据将如何安全地存储、管理和访问？</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-user-shield text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">隐私与安全</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">个人身份信息（PII）考量、匿名化、合规性（GDPR、HIPAA等）</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-search-dollar mr-2"></i>互动教学："数据侦探 - 缺失了什么？"
                        </div>
                        <p class="mb-2 font-medium">给定一个AI项目目标（例如："为电商网站开发AI驱动的个性化产品推荐器"）：</p>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>"这个AI绝对需要哪些3-5种'类型'的数据？"</li>
                            <li>"对于其中一种数据类型（例如，客户购买历史），哪些2-3个关键的'质量'属性对好的推荐至关重要？"</li>
                            <li>"使用这些数据存在哪些潜在的'隐私'问题？数据需求应如何解决这些问题？"</li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <!-- 模块三 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块三：需求文档化与多方评审 (45分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <div class="quote-box mb-6">
                        <div class="quote-content">
                            "沟通中最大的问题在于，人们想当然地认为沟通已经发生了。"
                        </div>
                        <div class="quote-author">— 乔治·萧伯纳</div>
                    </div>
                    
                    <p class="mb-6">
                        承前启后：我们已经理解了AI的独特性，对需求进行了分类。现在，我们如何正式地记录和验证这些需求？强调文档化的目的是为了在不同团队（业务、数据科学、工程、伦理、法律等）之间达成共识。
                    </p>
                    
                    <h4 class="text-xl font-semibold mb-4">3.1 AI需求文档的标准结构 (18分钟)</h4>
                    
                    <h5 class="font-bold text-lg mb-4">AI PRD/SRS的关键章节</h5>
                    <div class="card p-6 mb-6">
                        <ol class="space-y-3 text-gray-700 dark:text-gray-300">
                            <li>
                                <span class="font-bold">1. 引言</span>
                                <p class="text-sm ml-6">项目概述、目的、业务目标、价值主张</p>
                            </li>
                            <li>
                                <span class="font-bold">2. 范围</span>
                                <p class="text-sm ml-6">范围内/范围外、假设、依赖关系</p>
                            </li>
                            <li>
                                <span class="font-bold">3. 用户画像与故事 (AI适配版)</span>
                                <p class="text-sm ml-6">目标用户、AI特定的用户故事、概率性结果</p>
                            </li>
                            <li>
                                <span class="font-bold">4. 功能需求</span>
                                <p class="text-sm ml-6">AI能力、决策逻辑（如果高层次已知）、交互流程</p>
                            </li>
                            <li>
                                <span class="font-bold">5. 数据需求规格说明</span>
                                <p class="text-sm ml-6">数据来源、获取方式、数量、多样性、速度；数据质量指标与目标；数据预处理与转换；数据标注规范；数据存储、安全、隐私、合规性；数据血缘与治理</p>
                            </li>
                            <li>
                                <span class="font-bold">6. 非功能需求 (AI特定)</span>
                                <p class="text-sm ml-6">模型性能；可解释性需求；伦理需求；模型可维护性；鲁棒性与安全性；可扩展性、可用性、易用性</p>
                            </li>
                            <li>
                                <span class="font-bold">7. 成功指标与评估标准</span>
                                <p class="text-sm ml-6">业务KPIs、模型评估指标、验收标准</p>
                            </li>
                            <li>
                                <span class="font-bold">8. 运营考量</span>
                                <p class="text-sm ml-6">部署环境、监控、人机协同流程、交接</p>
                            </li>
                            <li>
                                <span class="font-bold">9. 伦理考量与影响评估</span>
                                <p class="text-sm ml-6">潜在危害、偏见缓解策略、社会影响</p>
                            </li>
                            <li>
                                <span class="font-bold">10. 术语表 / 附录</span>
                            </li>
                        </ol>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-sort mr-2"></i>互动教学："AI SRS/PRD - 章节排序与理由陈述"
                        </div>
                        <p class="mb-2">向学员提供一个包含典型及AI特定SRS/PRD章节标题的混乱列表。</p>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>"请将这些章节组织成一个逻辑清晰的AI项目SRS/PRD结构。"</li>
                            <li>"针对您所包含的2-3个AI特定章节（例如，伦理需求、数据质量），与传统软件项目相比，为何它们对AI项目尤为关键？"</li>
                        </ol>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">3.2 跨角色需求评审方法 (15分钟)</h4>
                    
                    <h5 class="font-bold text-lg mb-4">AI需求评审中的关键干系人</h5>
                    <div class="grid md:grid-cols-3 gap-4 mb-6">
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-user-tie text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">业务负责人/领域专家</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">验证业务目标、价值、功能范围、伦理一致性</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-brain text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">数据科学家/ML工程师</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">评估技术可行性、数据充分性、模型性能目标、XAI可行性</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-database text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">数据工程师/架构师</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">验证数据源可用性、质量、集成、安全性</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-gavel text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">法律/合规</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">检查法规遵从性、隐私、伦理风险</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-balance-scale text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">伦理专家（如有）</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">审查公平性、偏见、社会影响</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-user text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">最终用户（或代表）</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">验证易用性、与需求的契合度、输出/解释的可理解性</p>
                        </div>
                        
                        <div class="card p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-cogs text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">运营/IT</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">评估可部署性、可维护性、可扩展性</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-user-friends mr-2"></i>互动教学："干系人角色扮演评审"
                        </div>
                        <p class="mb-2">将学员分成小组，每组扮演一个干系人角色（业务负责人、数据科学家、法务、最终用户）。给他们一个简短的（有缺陷的）AI需求片段。</p>
                        <ol class="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                            <li>"从您的干系人视角出发，您对这个需求最主要的2个担忧或问题是什么？"</li>
                            <li>"您会提出什么样的澄清或修改建议？"</li>
                        </ol>
                        <p class="text-sm italic mt-2">（通过分享各"角色"的观点进行总结）</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">3.3 三类AI项目的需求差异 (12分钟)</h4>
                    
                    <div class="grid md:grid-cols-3 gap-6 mb-6">
                        <div class="card p-6">
                            <div class="text-center mb-4">
                                <i class="fas fa-robot text-3xl text-blue-500"></i>
                            </div>
                            <h5 class="text-lg font-bold text-center mb-4">智能体项目</h5>
                            <p class="text-sm mb-2"><span class="font-semibold">关键特征：</span>交互性、理解用户意图、对话管理、个性化</p>
                            <p class="text-sm"><span class="font-semibold">独特需求：</span>对话流程设计、意图识别准确率、回复相关性与自然度、角色一致性、歧义处理、上下文维护、知识库集成</p>
                        </div>
                        
                        <div class="card p-6">
                            <div class="text-center mb-4">
                                <i class="fas fa-cogs text-3xl text-blue-500"></i>
                            </div>
                            <h5 class="text-lg font-bold text-center mb-4">RPA项目</h5>
                            <p class="text-sm mb-2"><span class="font-semibold">关键特征：</span>自动化基于规则的任务，常利用AI处理异常或非结构化数据</p>
                            <p class="text-sm"><span class="font-semibold">独特需求：</span>流程定义准确性、异常处理规则、与现有系统的集成、OCR/NLP准确率、自动化操作的审计追踪、机器人的稳定性与可靠性</p>
                        </div>
                        
                        <div class="card p-6">
                            <div class="text-center mb-4">
                                <i class="fas fa-plug text-3xl text-blue-500"></i>
                            </div>
                            <h5 class="text-lg font-bold text-center mb-4">系统集成项目</h5>
                            <p class="text-sm mb-2"><span class="font-semibold">关键特征：</span>将AI模型嵌入到更大型的企业系统中</p>
                            <p class="text-sm"><span class="font-semibold">独特需求：</span>模型输入/输出的API规范、系统间数据映射、模型版本控制与部署策略、集成系统内模型性能的监控接口、安全性、可扩展性</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-project-diagram mr-2"></i>互动教学："需求连连看：匹配AI项目类型"
                        </div>
                        <p class="mb-4">展示一系列具体需求，例如：</p>
                        <ul class="space-y-2 text-gray-700 dark:text-gray-300 pl-5">
                            <li>• "聊天机器人必须在5轮对话内保持上下文连贯性"</li>
                            <li>• "RPA机器人从扫描PDF中提取发票号码的准确率必须达到99.9%"</li>
                            <li>• "欺诈检测API必须在200毫秒内响应"</li>
                        </ul>
                        <p class="mt-2">"此需求最主要属于哪种AI项目类型（智能体、RPA、系统集成）？为什么？"</p>
                    </div>
                </div>
            </div>
            
            <!-- 模块四 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块四：需求实战与应用 (45分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <div class="quote-box mb-6">
                        <div class="quote-content">
                            "一盎司的实践通常胜过一吨的理论。"
                        </div>
                        <div class="quote-author">— E. F. 舒马赫</div>
                    </div>
                    
                    <p class="mb-6">
                        从理论转向实践应用。本模块重在动手和巩固，通过实战练习让学员应用所学知识，规避常见陷阱，并了解需求与项目管理的衔接。
                    </p>
                    
                    <h4 class="text-xl font-semibold mb-4">4.1 规避常见需求陷阱 (12分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5 border-l-4 border-red-500">
                            <h5 class="font-bold mb-3 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                陷阱一：需求过度技术化
                            </h5>
                            <p class="text-sm mb-2"><span class="font-semibold">症状：</span>业务用户无法理解或验证。需求聚焦于算法，而非成果。</p>
                            <p class="text-sm"><span class="font-semibold">规避：</span>使用清晰、以业务为中心的语言。聚焦"做什么"和"为何做"，而非"如何做"。业务分析师在业务与技术间充当翻译。</p>
                        </div>
                        
                        <div class="card p-5 border-l-4 border-red-500">
                            <h5 class="font-bold mb-3 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                陷阱二：数据假设未经验证
                            </h5>
                            <p class="text-sm mb-2"><span class="font-semibold">症状：</span>项目启动后才发现数据不可用、质量差或有偏见，导致严重延误或失败。</p>
                            <p class="text-sm"><span class="font-semibold">规避：</span>早期数据评估。进行数据画像、小型概念验证（PoC）以测试数据效用。定义数据质量NFR。与数据团队共同验证假设。</p>
                        </div>
                        
                        <div class="card p-5 border-l-4 border-red-500">
                            <h5 class="font-bold mb-3 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                陷阱三：设定不合理的性能期望
                            </h5>
                            <p class="text-sm mb-2"><span class="font-semibold">症状：</span>从一开始就要求100%的准确率或近乎完美的性能。</p>
                            <p class="text-sm"><span class="font-semibold">规避：</span>向干系人普及AI的概率特性。从现实的基线开始。采用迭代开发。定义可接受的错误率及其业务影响。</p>
                        </div>
                        
                        <div class="card p-5 border-l-4 border-red-500">
                            <h5 class="font-bold mb-3 flex items-center">
                                <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                陷阱四：范围蔓延
                            </h5>
                            <p class="text-sm mb-2"><span class="font-semibold">症状：</span>无休止地调整模型或添加新数据源，缺乏明确目标。</p>
                            <p class="text-sm"><span class="font-semibold">规避：</span>强有力的初始范围定义。采用MVC方法。为AI项目建立稳健的变更控制流程（可将"研究型探索"视为计划内工作）。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-running mr-2"></i>互动教学："逃出陷阱！" - 快速场景应对
                        </div>
                        <p>针对每个陷阱，呈现一个团队即将陷入的极简场景。</p>
                        <p class="mt-2">"业务分析师或项目经理此刻应立即采取哪项关键行动来避免这个陷阱？"</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">4.2 实战练习：编写完整AI需求 (25分钟)</h4>
                    
                    <div class="card p-6 mb-6">
                        <h5 class="font-bold text-lg mb-4">练习简介</h5>
                        <p class="mb-4 text-gray-700 dark:text-gray-300">
                            提供一个场景（例如："为一家零售公司开发一个AI系统，用于向忠诚度计划会员推送个性化营销优惠"）。
                        </p>
                        
                        <h6 class="font-semibold mb-3">任务：小组合作，为该场景起草AI需求文档的关键部分</h6>
                        <ul class="space-y-2 text-gray-700 dark:text-gray-300 pl-5">
                            <li>• 1-2个业务目标</li>
                            <li>• 2-3个AI适配的用户故事</li>
                            <li>• 2-3项关键数据需求（来源、质量、隐私）</li>
                            <li>• 1-2项功能需求（概率性）</li>
                            <li>• 2-3项AI特定的NFR（例如：性能-准确率、可解释性、伦理-公平性）</li>
                            <li>• 1-2项成功指标（业务KPI、模型KPI）</li>
                        </ul>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-users-cog mr-2"></i>互动教学：小组需求起草练习
                        </div>
                        <p class="mb-2 font-medium">引导问题 (供辅导员在小组间使用)：</p>
                        <ul class="space-y-2 text-gray-700 dark:text-gray-300 pl-5">
                            <li>• "这个AI为零售公司解决的具体业务问题是什么？"</li>
                            <li>• "您将如何措辞用户故事，以反映AI的个性化能力和潜在的不确定性？"</li>
                            <li>• "关于客户和产品的哪些数据是必不可少的？需要达到什么质量水平？"</li>
                            <li>• "您将如何定义个性化优惠的'公平性'？如果AI只向高消费者提供高价值折扣——这公平吗？"</li>
                            <li>• "公司将如何判断这个AI是否成功（指标）？"</li>
                        </ul>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">4.3 需求到项目管理的衔接 (8分钟)</h4>
                    
                    <p class="mb-6 text-lg font-medium">需求是项目规划的基石。</p>
                    
                    <h5 class="font-bold text-lg mb-4">AI需求如何影响：</h5>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-project-diagram text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">项目范围</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">由业务目标、AI能力和MVC定义。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-sitemap text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">WBS (工作分解结构)</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">包括数据获取、准备、标注、模型训练、验证、伦理评审、XAI开发等任务（"区分确定性与探索性工作"）。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-calculator text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">估算</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">AI模型开发任务的不确定性更高。使用范围估算、时间盒探索。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">风险管理</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">AI特定的风险（数据偏见、模型漂移、伦理危害、低XAI）。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exchange-alt text-lg text-blue-500 mr-2"></i>
                                <h6 class="font-semibold">变更管理</h6>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">由于学习过程，需求演变的可能性高。需要敏捷方法。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-link mr-2"></i>互动教学："快速连接"
                        </div>
                        <p>"回顾您在练习(4.2)中起草的AI需求，请举例说明某项具体需求（例如，数据质量NFR或可解释性需求）将如何直接影响您规划'项目'的方式（例如，时间表、资源、风险）。"</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 课程二：AI项目管理 -->
        <section id="course2" class="mb-16">
            <h2 class="text-3xl font-bold mb-8 text-gradient">第二部分：《AI项目管理：从项目规划到价值实现》</h2>
            <p class="text-xl mb-8 text-gray-600 dark:text-gray-300 italic">总时长：3小时</p>
            
            <!-- 模块一 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块一：需求转化为项目计划 (45分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <h4 class="text-xl font-semibold mb-4">1.1 需求到行动计划的转换 (20分钟)</h4>
                    
                    <p class="mb-6">
                        本节将详细探讨如何将AI需求规范文档(SRS)作为项目计划的输入，转化为具体的行动计划，并区分AI项目中的确定性与探索性任务。
                    </p>
                    
                    <h5 class="font-bold text-lg mb-4">识别AI项目的主要阶段</h5>
                    <div class="timeline mb-6">
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h6 class="font-bold">数据获取与准备</h6>
                                <p class="text-sm text-gray-600 dark:text-gray-300">收集、清洗、标注、验证项目所需数据。</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h6 class="font-bold">模型开发与实验</h6>
                                <p class="text-sm text-gray-600 dark:text-gray-300">特征工程、算法选择、参数调优、实验迭代。</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h6 class="font-bold">模型验证与测试</h6>
                                <p class="text-sm text-gray-600 dark:text-gray-300">性能评估、公平性测试、用户验收测试。</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h6 class="font-bold">部署</h6>
                                <p class="text-sm text-gray-600 dark:text-gray-300">模型部署、集成、上线前测试。</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h6 class="font-bold">监控与迭代</h6>
                                <p class="text-sm text-gray-600 dark:text-gray-300">性能监控、漂移检测、再训练、持续优化。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-sitemap mr-2"></i>互动教学："AI WBS挑战赛"
                        </div>
                        <p>提供一组AI需求（可延续第一门课的案例），让学员分组起草一个高层次的WBS，并特别标注出哪些任务更具探索性。</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">1.2 AI项目的特殊规划要素 (15分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-database text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">数据工作流的前置规划</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">数据收集、清洗、标注、存储、版本控制的完整流程。这是AI项目成功的基础，需要在项目早期就进行详细规划。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-microscope text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">探索-验证-实施的阶段设计</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">明确实验阶段的目标、指标和决策点。AI项目通常需要多次实验和迭代才能找到最佳方案。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-users-cog text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">基于需求的资源配置策略</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">数据科学家、机器学习工程师、领域专家等特殊角色的早期介入和协作是AI项目特有的资源配置需求。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-balance-scale text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">伦理审查与合规检查点</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">在项目计划中预设的评审环节，确保AI系统的伦理性和合规性，这是传统项目中不常见的规划要素。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-exchange-alt mr-2"></i>互动教学："AI规划要素对对碰"
                        </div>
                        <p>列出典型的项目管理任务（如风险评估、资源规划、沟通计划），提问学员，AI的特性会如何改变这些任务的方法，或增加哪些新的考虑因素。</p>
                    </div>
                </div>
            </div>
            
            <!-- 模块二 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块二：AI项目执行与控制 (45分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <h4 class="text-xl font-semibold mb-4">2.1 管理模型 (10分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-wave-square text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">传统瀑布模型的局限性</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">在AI项目中，传统瀑布模型难以应对不确定性和需求变化，因为AI开发过程本质上是探索性和迭代性的。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-sync-alt text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">敏捷原则在AI项目中的适用性</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">迭代、增量、快速反馈、拥抱变化等敏捷原则非常适合AI项目的特性，能够更好地应对AI开发中的不确定性。</p>
                        </div>
                    </div>
                    
                    <div class="card p-5 mb-6">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-train text-2xl text-indigo-500 mr-3"></i>
                            <h5 class="font-bold">双轨敏捷 (Dual-Track Agile)</h5>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300">区分探索性工作轨道（实验、学习）和确定性工作轨道（工程实现、部署）。这种方法特别适合AI项目，因为AI项目通常同时包含高度探索性的研究工作和确定性的工程工作。</p>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-puzzle-piece mr-2"></i>互动教学："模型匹配挑战"
                        </div>
                        <p>提供几个简短的AI项目场景描述，让学员讨论哪种管理模型（或混合模型）最适合，并说明理由。</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">2.2 迭代与适应性管理 (20分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-sync text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">短迭代周期</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">设计适合AI项目的短迭代周期（例如1-2周），快速验证假设和收集反馈，尤其是在模型开发的探索阶段。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-chart-line text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">定期性能评审</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">定期进行模型性能评审和业务价值评估，确保项目朝着正确的方向发展，并及时调整策略。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-comments text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">反馈循环</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">建立有效的反馈循环：来自用户、干系人以及模型自身的性能数据，这些反馈是项目迭代和改进的重要依据。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-exchange-alt text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">变更管理</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">AI项目中的变更管理：区分有价值的演进与无谓的范围蔓延，为AI项目特有的探索性质设计适当的变更管理流程。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-users mr-2"></i>互动教学："AI回顾会议模拟"
                        </div>
                        <p>模拟一个AI项目的Sprint评审会议，其中模型未能达到预期目标。引导学员讨论团队应如何在下一个Sprint中进行调整和适应。</p>
                    </div>
                </div>
            </div>
            
            <!-- 模块三 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块三：多项目与资源管理 (45分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <h4 class="text-xl font-semibold mb-4">3.1 不同类型AI项目的管理重点 (20分钟)</h4>
                    
                    <div class="grid md:grid-cols-3 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-robot text-3xl text-indigo-500"></i>
                            </div>
                            <h5 class="text-lg font-bold text-center mb-4">智能体项目管理重点</h5>
                            <ul class="text-sm space-y-2 text-gray-600 dark:text-gray-300">
                                <li>• 管理对话设计迭代</li>
                                <li>• 用户反馈循环</li>
                                <li>• 知识库更新</li>
                                <li>• 意图识别准确率的持续优化</li>
                            </ul>
                        </div>
                        
                        <div class="card p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-cogs text-3xl text-indigo-500"></i>
                            </div>
                            <h5 class="text-lg font-bold text-center mb-4">RPA项目管理重点</h5>
                            <ul class="text-sm space-y-2 text-gray-600 dark:text-gray-300">
                                <li>• 保障流程稳定性</li>
                                <li>• 管理机器人部署与更新</li>
                                <li>• 监控可能破坏机器人的流程变更</li>
                                <li>• 利用AI进行异常处理的有效性</li>
                            </ul>
                        </div>
                        
                        <div class="card p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-plug text-3xl text-indigo-500"></i>
                            </div>
                            <h5 class="text-lg font-bold text-center mb-4">集成项目管理重点</h5>
                            <ul class="text-sm space-y-2 text-gray-600 dark:text-gray-300">
                                <li>• 管理与其他系统的依赖关系</li>
                                <li>• API版本控制</li>
                                <li>• 协调测试</li>
                                <li>• 保障数据流完整性</li>
                                <li>• 监控集成后AI模型的性能</li>
                            </ul>
                        </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-fire-alt mr-2"></i>互动教学："AI项目管理热点问题"
                        </div>
                        <p>为每种AI项目类型设计一个典型的问题场景。提问学员："作为项目经理，您应对此问题的首要任务是什么？"</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">3.2 资源优化与知识共享 (15分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-user-graduate text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">管理稀缺的AI人才资源</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">数据科学家、ML工程师等人才的合理分配与利用，这些专业人才通常是稀缺资源，需要特别关注如何最有效地利用他们的时间和专长。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-sort-amount-up text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">为高价值资源进行项目优先级排序</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">基于业务价值和资源稀缺性的战略性资源分配，确保最关键的项目获得足够的AI专业人才支持。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-share-alt text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">协作与知识管理工具</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">代码库、模型注册库、文档平台、共享知识库等工具对于AI团队的协作和知识积累至关重要。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-recycle text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">创建可复用组件</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">如数据预处理脚本、特征工程库、模型组件等可复用组件可以大大提高团队效率，减少重复工作。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-brain mr-2"></i>互动教学："AI项目知识传承头脑风暴"
                        </div>
                        <p>分组讨论："一个AI项目结束后，哪些关键知识和经验应该被总结和分享，以便后续项目受益？"</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">3.3 多项目的协调机制 (10分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-project-diagram text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">AI项目群管理的概念</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">整合管理多个相关AI项目，实现战略协同，这对于企业全面推进AI转型尤为重要。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-clipboard-list text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">标准化的AI项目报告和监控方法</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">统一的指标、报告格式和健康状况评估标准，便于跨项目比较和资源分配决策。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-shield-alt text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">跨项目的风险识别与缓解</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">共同风险的识别、共享与集中缓解策略，避免多个项目重复犯同样的错误。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-building text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">AI治理委员会或AI卓越中心 (CoE)</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">负责战略、标准、治理、知识共享的专门组织单元，是企业实现AI规模化和标准化的重要机制。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-balance-scale-right mr-2"></i>互动教学："AI CoE的利弊分析"
                        </div>
                        <p>引导学员讨论设立一个集中的AI卓越中心（CoE）来管理多个AI项目可能带来的好处和挑战。</p>
                    </div>
                </div>
            </div>
            
            <!-- 模块四 -->
            <div class="module-card mb-8">
                <div class="module-header" onclick="toggleModule(this)">
                    <span class="text-xl">模块四：项目收尾与价值评估 (45分钟)</span>
                    <i class="fas fa-chevron-down text-gray-500"></i>
                </div>
                <div class="module-content">
                    <h4 class="text-xl font-semibold mb-4">4.1 AI系统的部署与转交 (10分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-rocket text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">AI系统部署策略</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">灰度发布、蓝绿部署，视AI服务特性而定。AI系统的部署通常需要特别小心，以确保模型在生产环境中的稳定性。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-exchange-alt text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">向运营团队交接</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">详细的文档、对支持团队的培训。AI系统的交接比传统系统更复杂，需要特别关注模型监控和维护方面的知识转移。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-chart-line text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">模型持续监控和维护</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">明确的责任分工和流程，确保模型部署后能够被有效监控，及时发现并处理模型漂移等问题。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-users text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">用户培训和组织变革管理</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">确保用户接受和有效使用AI系统，这对于实现系统预期价值至关重要，尤其是当AI改变了现有工作流程时。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-clipboard-check mr-2"></i>互动教学："AI系统交接清单拟定"
                        </div>
                        <p>让学员分组讨论并列出："向运营团队交接一个AI系统时，最重要的5个清单项目是什么？"</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">4.2 价值评估与经验总结 (15分钟)</h4>
                    
                    <div class="grid md:grid-cols-2 gap-6 mb-6">
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-chart-pie text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">衡量业务价值与KPIs</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">实际达成的业务价值与初始设定的KPIs的对比，这是评估AI项目成功与否的关键依据。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-calculator text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">计算AI项目的投资回报率 (ROI)</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">挑战在于准确归因价值。AI项目的ROI计算通常比传统项目更复杂，需要考虑直接和间接的价值创造。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-lightbulb text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">评估定性价值</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">例如，获得的新能力、改进的决策质量、提升的创新能力等难以量化但同样重要的价值。</p>
                        </div>
                        
                        <div class="card p-5">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-book-open text-2xl text-indigo-500 mr-3"></i>
                                <h5 class="font-bold">组织"经验教训总结"会议</h5>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300">回顾项目中哪些做法有效，哪些无效，特别是针对AI的特定方面（数据、模型、伦理、可解释性等），为未来项目积累经验。</p>
                        </div>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-chart-bar mr-2"></i>互动教学："AI项目ROI的难点辨析"
                        </div>
                        <p>引导学员讨论量化AI项目ROI时可能遇到的挑战（例如，如何将AI的影响与其他因素区分开，如何评估探索性工作的价值等）。</p>
                    </div>
                    
                    <h4 class="text-xl font-semibold mt-8 mb-4">4.3 综合实战演练 (20分钟)</h4>
                    
                    <div class="card p-6 mb-6">
                        <h5 class="font-bold text-lg mb-4">顶点项目练习</h5>
                        <p class="mb-4 text-gray-700 dark:text-gray-300">
                            作为课程的总结性练习，提供一个AI项目从初始概念到（模拟的）部署后评审的完整场景。学员分组在项目的不同阶段做出关键的项目管理决策。
                        </p>
                    </div>
                    
                    <div class="activity-card">
                        <div class="activity-title">
                            <i class="fas fa-project-diagram mr-2"></i>互动教学："AI项目生命周期模拟推演"
                        </div>
                        <p>每个小组负责一个简化的AI项目。他们需要识别关键需求，勾勒项目计划框架，识别2-3个主要风险，并定义如何衡量项目成功。最后进行简短的小组分享和讨论。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 结论与建议 -->
        <section id="conclusion" class="mb-16">
            <h2 class="text-3xl font-bold mb-8 text-gradient">结论与建议</h2>
            
            <div class="card p-8">
                <p class="mb-6 first-letter-drop">
                    本教学手册旨在为企业业务人员提供一套系统化、互动式的AI需求规范与项目管理课程。通过本手册的指导，讲师可以帮助学员理解AI项目的独特性，掌握面向AI的结构化方法论，并通过实践与应用将理论知识转化为实际能力。
                </p>
                
                <div class="grid md:grid-cols-2 gap-8 mb-8">
                    <div class="card p-6 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                                <i class="fas fa-fingerprint"></i>
                            </div>
                            <h4 class="text-lg font-bold">理解AI的独特性</h4>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300">
                            帮助学员理解AI技术在需求定义、数据依赖、概率性输出、持续学习、伦理考量等方面的根本差异，打破传统IT项目的思维定式。
                        </p>
                    </div>
                    
                    <div class="card p-6 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <h4 class="text-lg font-bold">构建结构化方法论</h4>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300">
                            引导学员掌握从业务目标到AI能力，再到具体功能与非功能需求（特别是AI特有的数据、性能、可解释性、伦理和模型可维护性需求）的系统化方法。
                        </p>
                    </div>
                    
                    <div class="card p-6 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                                <i class="fas fa-hands-helping"></i>
                            </div>
                            <h4 class="text-lg font-bold">强调实践与应用</h4>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300">
                            通过案例分析、情景模拟和实战演练，使学员能够将理论知识应用于解决实际问题，规避常见陷阱，并初步具备撰写和评审AI需求的能力。
                        </p>
                    </div>
                    
                    <div class="card p-6 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h4 class="text-lg font-bold">需求到项目管理的衔接</h4>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300">
                            明确AI需求如何驱动项目规划、执行和控制，为学员学习后续的AI项目管理课程打下坚实基础，确保端到端的项目成功。
                        </p>
                    </div>
                </div>
                
                <div class="p-6 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20 rounded-lg mb-8">
                    <h4 class="text-lg font-bold mb-4">讲师建议</h4>
                    <p class="mb-4 text-gray-700 dark:text-gray-300">
                        建议讲师在授课过程中，灵活运用本手册提供的互动教学设计，鼓励学员积极参与、深度思考，并结合学员所在企业的实际情况进行案例的调整和引申，以期达到最佳的教学效果，真正赋能业务人员驾驭AI转型带来的机遇与挑战。
                    </p>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-user-edit text-blue-500 mr-2"></i>
                                <p class="font-semibold">个性化案例</p>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">针对学员所在行业定制案例</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-users text-blue-500 mr-2"></i>
                                <p class="font-semibold">小组多样性</p>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">确保小组成员背景多元</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-handshake text-blue-500 mr-2"></i>
                                <p class="font-semibold">鼓励合作</p>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">培养跨职能团队协作意识</p>
                        </div>
                    </div>
                </div>
                
                <h4 class="text-lg font-bold mb-4">延伸阅读</h4>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="card p-5 hover:shadow-md transition-shadow">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-4">
                                <i class="fas fa-book text-3xl text-blue-500"></i>
                            </div>
                            <div>
                                <h5 class="font-semibold mb-1">《商业分析实践指南》(BABOK Guide)</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-300">国际商业分析师协会 (IIBA)</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">理解传统需求分析的基础知识</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card p-5 hover:shadow-md transition-shadow">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-4">
                                <i class="fas fa-book text-3xl text-blue-500"></i>
                            </div>
                            <div>
                                <h5 class="font-semibold mb-1">《项目管理知识体系指南》(PMBOK Guide)</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-300">项目管理协会 (PMI)</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">掌握项目管理的标准方法论</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card p-5 hover:shadow-md transition-shadow">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-4">
                                <i class="fas fa-book text-3xl text-blue-500"></i>
                            </div>
                            <div>
                                <h5 class="font-semibold mb-1">《人工智能项目管理：从理念到实践》</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Adrien Gaidon, et al.</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">AI项目的实用管理技术与案例</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card p-5 hover:shadow-md transition-shadow">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-4">
                                <i class="fas fa-book text-3xl text-blue-500"></i>
                            </div>
                            <div>
                                <h5 class="font-semibold mb-1">《负责任的AI实践》</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Microsoft AI Ethics Team</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">AI伦理考量与责任实践指南</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 回到顶部按钮 -->
    <div class="back-to-top" id="back-to-top" title="回到顶部">
        <i class="fas fa-arrow-up"></i>
    </div>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-8 md:mb-0">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-graduation-cap text-2xl text-blue-400 mr-3"></i>
                        <span class="font-bold text-xl">AI转型教学中心</span>
                    </div>
                    <p class="text-gray-400 max-w-md">
                        提供专业的AI需求规范与项目管理培训，帮助企业业务人员成功驾驭AI转型之旅，从业务愿景到价值落地。
                    </p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
                    <div>
                        <h4 class="font-bold text-lg mb-4">快速链接</h4>
                        <ul class="space-y-2">
                            <li><a href="#overview" class="text-gray-400 hover:text-white transition-colors">课程总览</a></li>
                            <li><a href="#course1" class="text-gray-400 hover:text-white transition-colors">需求规范</a></li>
                            <li><a href="#course2" class="text-gray-400 hover:text-white transition-colors">项目管理</a></li>
                            <li><a href="#conclusion" class="text-gray-400 hover:text-white transition-colors">结论建议</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-bold text-lg mb-4">相关资源</h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">案例库</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">工具模板</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">研究报告</a></li>
                            <li><a href="#" class="text-gray-400 hover:text-white transition-colors">行业洞察</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-bold text-lg mb-4">联系我们</h4>
                        <ul class="space-y-2">
                            <li class="flex items-center">
                                <i class="fas fa-envelope text-gray-400 mr-2"></i>
                                <span class="text-gray-400"><EMAIL></span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-phone text-gray-400 mr-2"></i>
                                <span class="text-gray-400">+86 123 4567 8910</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-500">
                <p>&copy; 2025 AI转型教学中心. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 深色模式切换
        function initTheme() {
            const themeToggle = document.getElementById('theme-toggle');
            const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
            const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
            
            // 检查本地存储或系统偏好
            const savedTheme = localStorage.getItem('theme');
            let currentTheme = savedTheme || (prefersDarkScheme.matches ? 'dark' : 'light');
            
            // 应用主题
            document.documentElement.setAttribute('data-theme', currentTheme);
            if (currentTheme === 'dark') {
                themeToggle.classList.add('dark');
                if (mobileThemeToggle) mobileThemeToggle.classList.add('dark');
            }
            
            // 切换事件
            function toggleTheme() {
                const newTheme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                if (newTheme === 'dark') {
                    themeToggle.classList.add('dark');
                    if (mobileThemeToggle) mobileThemeToggle.classList.add('dark');
                } else {
                    themeToggle.classList.remove('dark');
                    if (mobileThemeToggle) mobileThemeToggle.classList.remove('dark');
                }
            }
            
            themeToggle.addEventListener('click', toggleTheme);
            if (mobileThemeToggle) mobileThemeToggle.addEventListener('click', toggleTheme);
        }
        
        // 模块切换
        function toggleModule(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('i');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                content.classList.add('active');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        }
        
        // 移动端菜单
        function initMobileMenu() {
            const menuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            
            if (menuButton && mobileMenu) {
                menuButton.addEventListener('click', function() {
                    if (mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.remove('hidden');
                    } else {
                        mobileMenu.classList.add('hidden');
                    }
                });
            }
        }
        
        // 回到顶部按钮
        function initBackToTop() {
            const backToTop = document.getElementById('back-to-top');
            
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('visible');
                } else {
                    backToTop.classList.remove('visible');
                }
            });
            
            backToTop.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
        
        // 平滑滚动
        function initSmoothScroll() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    
                    // 如果是移动菜单，点击后隐藏菜单
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                    
                    const targetId = this.getAttribute('href');
                    if (targetId !== '#') {
                        const targetElement = document.querySelector(targetId);
                        if (targetElement) {
                            targetElement.scrollIntoView({
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        }
        
        // 初始化Mermaid图表
        function initMermaid() {
            mermaid.initialize({
                startOnLoad: true,
                theme: document.documentElement.getAttribute('data-theme') === 'dark' ? 'dark' : 'default',
                securityLevel: 'loose',
                logLevel: 5
            });
        }
        
        // 页面加载完成后初始化所有功能
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
            initMobileMenu();
            initBackToTop();
            initSmoothScroll();
            initMermaid();
        });
    </script>
</body>
</html>