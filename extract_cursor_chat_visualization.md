# Extract Cursor Chat - Functionality Visualization

## Data Flow Diagram

```mermaid
graph TD
    A[Cursor Editor] --> B[Workspace DB<br>state.vscdb]
    A --> C[Session DB<br>state.sqlite]
    
    B --> D[extract_project]
    C --> E[extract_messages]
    
    D --> F[Project Metadata<br>name, rootPath]
    E --> G[Chat Messages<br>role, content]
    
    F --> H[ChatSession]
    G --> H
    
    H --> I[JSON Output]
    
    style A fill:#f9d77e
    style B fill:#a8d1ff
    style C fill:#a8d1ff
    style H fill:#c9e7c8
    style I fill:#ffc0cb
```

## Data Structure

```mermaid
classDiagram
    class ChatSession {
        +Dict project
        +List messages
        +to_dict()
    }
    
    class Project {
        +String name
        +String rootPath
    }
    
    class Message {
        +String role
        +String content
    }
    
    ChatSession "1" --> "1" Project: contains
    ChatSession "1" --> "*" Message: contains
```

## Process Flow

```mermaid
sequenceDiagram
    participant User
    participant <PERSON>rip<PERSON> as extract_cursor_chat.py
    participant WorkspaceDB as state.vscdb
    participant SessionDB as state.sqlite
    participant Output as JSON File
    
    User->>Script: Run with parameters
    Script->>WorkspaceDB: Query for project data
    WorkspaceDB-->>Script: Return history.entries
    Script->>Script: Process file paths
    Script->>Script: Extract project name & root
    
    Script->>SessionDB: Query for chat messages
    SessionDB-->>Script: Return bubbleId entries
    Script->>Script: Parse message content & roles
    Script->>Script: Sort messages by insertion order
    
    Script->>Script: Create ChatSession object
    Script->>Output: Write JSON data
    Output-->>User: Provide extracted chat history
```

## Key Functions

| Function | Purpose |
|----------|---------|
| `extract_project()` | Extracts project metadata from workspace database |
| `_iter_bubble_messages()` | Iterates through bubble messages in session database |
| `extract_messages()` | Extracts and sorts all messages from session database |
| `load_chat_session()` | Combines project and messages into a ChatSession |

## Command Line Usage

```bash
python extract_cursor_chat.py --workspace state.vscdb --session state.sqlite --out chat.json
```

## Integration with Other Scripts

The script is also used by `cursor_chat_finder.py` which:
1. Automatically locates all workspace and session databases
2. Extracts chat data from all found databases
3. Combines results into a comprehensive collection of chat sessions
